#!/bin/bash
# Script to run the Bisq Telegram Trading Bot

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Python 3 is not installed. Please install Python 3 and try again."
    exit 1
fi

# Check if required packages are installed
python3 -c "import telegram, grpc" &> /dev/null
if [ $? -ne 0 ]; then
    echo "Installing required packages..."
    pip3 install python-telegram-bot grpcio grpcio-tools
fi

# Run the bot
echo "Starting Bisq Telegram Trading Bot..."
python3 bot.py

