#!/bin/bash
# Installation script for the Bisq Telegram Trading Bot

echo "Installing Bisq Telegram Trading Bot..."

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed. Please install Python 3 and try again."
    exit 1
fi

# Check Python version
python_version=$(python3 -c 'import sys; print(f"{sys.version_info.major}.{sys.version_info.minor}")')
required_version="3.9"
if [[ $(echo "$python_version < $required_version" | bc) -eq 1 ]]; then
    echo "Error: Python $required_version or higher is required. You have Python $python_version."
    exit 1
fi

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "Error: pip3 is not installed. Please install pip3 and try again."
    exit 1
fi

# Install dependencies
echo "Installing dependencies..."
pip3 install -r requirements.txt

# Make scripts executable
chmod +x run.sh
chmod +x test.py

echo "Running tests..."
python3 test.py

if [ $? -eq 0 ]; then
    echo "Installation completed successfully!"
    echo "You can now run the bot using: ./run.sh"
    echo ""
    echo "Before running the bot, make sure to:"
    echo "1. Start the Bisq daemon with API enabled"
    echo "2. Update the bot configuration in bot.py"
    echo ""
    echo "For more information, see the README.md file."
else
    echo "Tests failed. Please check the error messages above."
    exit 1
fi

