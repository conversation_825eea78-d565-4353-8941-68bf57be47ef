#!/usr/bin/env python3
"""
Setup script for the Bisq Telegram Trading Bot.
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = fh.read().splitlines()

setup(
    name="bisq-telegram-bot",
    version="1.0.0",
    author="Trading Bot Developer",
    author_email="<EMAIL>",
    description="A Telegram bot that interfaces with Bisq for automated cryptocurrency trading",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/bisq-telegram-bot",
    packages=find_packages(),
    classifiers=[
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.9",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "bisq-telegram-bot=bot:main",
        ],
    },
)

