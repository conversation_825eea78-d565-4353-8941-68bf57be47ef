#!/usr/bin/env python3
"""
Bisq Telegram Trading Bot - Main Entry Point

This script integrates the Telegram bot with the Bisq trading functionality.
"""

import logging
import asyncio
import os
import sys
from datetime import datetime
from typing import Dict, Any, Optional

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import (
    Application,
    CommandHandler,
    ContextTypes,
    CallbackQueryHandler,
    ConversationHandler,
)

from bisq_client import BisqClient
from trading_strategy import TradingStrategy

# Configure logging
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Bot configuration
TELEGRAM_TOKEN = "**********************************************"
BISQ_API_HOST = "localhost"
BISQ_API_PORT = "9998"  # Default Bisq API port
BISQ_API_PASSWORD = "api_password"  # Replace with your actual Bisq API password

# Define conversation states
MAIN_MENU, SETTINGS = range(2)

class BisqTradingBot:
    """Main class for the Bisq Telegram Trading Bot."""
    
    def __init__(self, token: str, bisq_host: str, bisq_port: str, bisq_password: str):
        """Initialize the bot.
        
        Args:
            token: Telegram bot token
            bisq_host: Bisq API host
            bisq_port: Bisq API port
            bisq_password: Bisq API password
        """
        self.token = token
        self.bisq_host = bisq_host
        self.bisq_port = bisq_port
        self.bisq_password = bisq_password
        
        # Initialize Bisq client
        self.bisq_client = BisqClient(host=bisq_host, port=bisq_port, password=bisq_password)
        
        # Initialize trading strategy
        self.strategy = TradingStrategy(self.bisq_client, initial_balance=1.0, max_duration_minutes=60)
        
        # Initialize Telegram application
        self.application = Application.builder().token(token).build()
        
        # Set up handlers
        self._setup_handlers()
    
    def _setup_handlers(self) -> None:
        """Set up Telegram command and callback handlers."""
        # Set up conversation handler
        conv_handler = ConversationHandler(
            entry_points=[CommandHandler("start", self.start)],
            states={
                MAIN_MENU: [CallbackQueryHandler(self.button_handler)],
                SETTINGS: [CallbackQueryHandler(self.button_handler)],
            },
            fallbacks=[CommandHandler("start", self.start)],
        )
        
        # Add conversation handler and other command handlers
        self.application.add_handler(conv_handler)
        self.application.add_handler(CommandHandler("help", self.help_command))
        self.application.add_handler(CommandHandler("status", self.status_command))
        self.application.add_handler(CommandHandler("history", self.history_command))
    
    async def start(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
        """Send a welcome message when the command /start is issued."""
        user = update.effective_user
        
        # Get strategy status
        status = await self.strategy.get_status()
        
        keyboard = [
            [
                InlineKeyboardButton("Start Bot", callback_data="start_bot"),
                InlineKeyboardButton("Stop Bot", callback_data="stop_bot"),
            ],
            [
                InlineKeyboardButton("Check Balance", callback_data="check_balance"),
                InlineKeyboardButton("Settings", callback_data="settings"),
            ],
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_html(
            f"Hi {user.mention_html()}!\n\n"
            f"Welcome to the Bisq Trading Bot. This bot implements a 'buy low, sell high' "
            f"strategy for short contracts on Bisq.\n\n"
            f"Current Status: {'Running' if status['is_running'] else 'Stopped'}\n"
            f"Initial Balance: ${status['initial_balance']:.2f}\n"
            f"Current Balance: ${status['current_balance']:.2f}",
            reply_markup=reply_markup,
        )
        
        return MAIN_MENU
    
    async def button_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
        """Handle button presses."""
        query = update.callback_query
        await query.answer()
        
        if query.data == "start_bot":
            # Start the trading strategy
            status = await self.strategy.get_status()
            
            if not status["is_running"]:
                await self.strategy.start()
                # Start the trading loop in the background
                context.application.create_task(self.trading_loop())
                await query.edit_message_text(
                    text="Bot started! The trading algorithm is now running."
                )
            else:
                await query.edit_message_text(
                    text="Bot is already running!"
                )
            return MAIN_MENU
        
        elif query.data == "stop_bot":
            # Stop the trading strategy
            status = await self.strategy.get_status()
            
            if status["is_running"]:
                await self.strategy.stop()
                await query.edit_message_text(
                    text="Bot stopped! The trading algorithm has been halted."
                )
            else:
                await query.edit_message_text(
                    text="Bot is already stopped!"
                )
            return MAIN_MENU
        
        elif query.data == "check_balance":
            # Get the latest balance from the strategy
            status = await self.strategy.get_status()
            
            profit_loss = status["profit_loss"]
            profit_percentage = status["profit_percentage"]
            
            # Format recent trades
            recent_trades_text = ""
            if status["recent_trades"]:
                for trade in status["recent_trades"]:
                    action = trade["action"].upper()
                    amount_btc = trade["amount_btc"]
                    price = trade["price"]
                    amount_usd = trade["amount_usd"]
                    timestamp = trade["timestamp"].strftime("%Y-%m-%d %H:%M:%S")
                    
                    if action == "BUY":
                        recent_trades_text += f"- {timestamp}: Bought {amount_btc:.8f} BTC at ${price:.2f}\n"
                    else:  # SELL
                        recent_trades_text += f"- {timestamp}: Sold {amount_btc:.8f} BTC for ${amount_usd:.2f}\n"
            else:
                recent_trades_text = "No recent trades"
            
            await query.edit_message_text(
                text=f"💰 *Balance Information* 💰\n\n"
                     f"Initial Balance: ${status['initial_balance']:.2f}\n"
                     f"Current Balance: ${status['current_balance']:.2f}\n"
                     f"BTC Balance: {status['btc_balance']:.8f} BTC\n"
                     f"Profit/Loss: ${profit_loss:.2f} ({profit_percentage:.2f}%)\n\n"
                     f"Current Position: {status['current_position'].upper()}\n"
                     f"Current BTC Price: ${status['current_price']:.2f}\n\n"
                     f"Bot Status: {'Running' if status['is_running'] else 'Stopped'}\n"
                     f"Time Remaining: {status['time_remaining_minutes']:.1f} minutes\n\n"
                     f"Recent Trades:\n{recent_trades_text}",
                parse_mode="Markdown"
            )
            return MAIN_MENU
        
        elif query.data == "settings":
            keyboard = [
                [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_main")],
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await query.edit_message_text(
                text="⚙️ *Settings* ⚙️\n\n"
                     "Configure your trading parameters here.",
                reply_markup=reply_markup,
                parse_mode="Markdown"
            )
            return SETTINGS
        
        elif query.data == "back_to_main":
            # Return to main menu
            status = await self.strategy.get_status()
            
            keyboard = [
                [
                    InlineKeyboardButton("Start Bot", callback_data="start_bot"),
                    InlineKeyboardButton("Stop Bot", callback_data="stop_bot"),
                ],
                [
                    InlineKeyboardButton("Check Balance", callback_data="check_balance"),
                    InlineKeyboardButton("Settings", callback_data="settings"),
                ],
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await query.edit_message_text(
                text=f"Main Menu\n\n"
                     f"Current Status: {'Running' if status['is_running'] else 'Stopped'}\n"
                     f"Initial Balance: ${status['initial_balance']:.2f}\n"
                     f"Current Balance: ${status['current_balance']:.2f}",
                reply_markup=reply_markup
            )
            return MAIN_MENU
        
        return MAIN_MENU
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Send a message when the command /help is issued."""
        help_text = (
            "🤖 *Bisq Trading Bot Help* 🤖\n\n"
            "This bot implements a 'buy low, sell high' strategy for short contracts on Bisq.\n\n"
            "*Available Commands:*\n"
            "/start - Start the bot and show the main menu\n"
            "/help - Show this help message\n"
            "/status - Show current bot status and balance\n"
            "/history - Show recent trading history\n\n"
            "*How it works:*\n"
            "The bot monitors the market for profitable opportunities and executes trades "
            "automatically when the conditions are right. It aims to maximize profits within "
            "an hour starting with $1.00."
        )
        
        await update.message.reply_markdown(help_text)
    
    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Send current bot status when the command /status is issued."""
        status = await self.strategy.get_status()
        
        profit_loss = status["profit_loss"]
        profit_percentage = status["profit_percentage"]
        
        status_text = (
            "📊 *Bot Status* 📊\n\n"
            f"Bot is currently: {'🟢 Running' if status['is_running'] else '🔴 Stopped'}\n"
            f"Initial Balance: ${status['initial_balance']:.2f}\n"
            f"Current Balance: ${status['current_balance']:.2f}\n"
            f"BTC Balance: {status['btc_balance']:.8f} BTC\n"
            f"Profit/Loss: ${profit_loss:.2f} ({profit_percentage:.2f}%)\n\n"
            f"Current Position: {status['current_position'].upper()}\n"
            f"Current BTC Price: ${status['current_price']:.2f}\n\n"
        )
        
        if status["is_running"] and status["time_remaining_minutes"] is not None:
            status_text += f"Time Remaining: {status['time_remaining_minutes']:.1f} minutes\n"
        
        await update.message.reply_markdown(status_text)
    
    async def history_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Send trading history when the command /history is issued."""
        status = await self.strategy.get_status()
        
        if not status["recent_trades"]:
            await update.message.reply_text("No trading history available yet.")
            return
        
        history_text = "📜 *Trading History* 📜\n\n"
        
        for i, trade in enumerate(status["recent_trades"], 1):
            action = trade["action"].upper()
            amount_btc = trade["amount_btc"]
            price = trade["price"]
            amount_usd = trade["amount_usd"]
            fee_usd = trade["fee_usd"]
            timestamp = trade["timestamp"].strftime("%Y-%m-%d %H:%M:%S")
            
            if action == "BUY":
                history_text += (
                    f"{i}. {timestamp} - BUY\n"
                    f"   Amount: {amount_btc:.8f} BTC\n"
                    f"   Price: ${price:.2f}\n"
                    f"   Total: ${amount_usd:.2f}\n"
                    f"   Fee: ${fee_usd:.2f}\n\n"
                )
            else:  # SELL
                history_text += (
                    f"{i}. {timestamp} - SELL\n"
                    f"   Amount: {amount_btc:.8f} BTC\n"
                    f"   Price: ${price:.2f}\n"
                    f"   Total: ${amount_usd:.2f}\n"
                    f"   Fee: ${fee_usd:.2f}\n\n"
                )
        
        await update.message.reply_markdown(history_text)
    
    async def trading_loop(self) -> None:
        """Main trading loop that runs in the background."""
        logger.info("Trading loop started")
        
        while True:
            try:
                # Get strategy status
                status = await self.strategy.get_status()
                
                if not status["is_running"]:
                    logger.info("Trading strategy is not running, exiting loop")
                    break
                
                # Update strategy
                await self.strategy.update()
                
                # Wait before next iteration
                await asyncio.sleep(60)  # Update every minute
                
            except Exception as e:
                logger.error(f"Error in trading loop: {e}")
                await asyncio.sleep(30)  # Wait before retrying
        
        logger.info("Trading loop stopped")
    
    def run(self) -> None:
        """Start the bot."""
        # Start the Bot
        self.application.run_polling(allowed_updates=Update.ALL_TYPES)
    
    def close(self) -> None:
        """Close the bot and clean up resources."""
        # Close Bisq client
        self.bisq_client.close()

def main() -> None:
    """Main entry point."""
    # Create and run the bot
    bot = BisqTradingBot(
        token=TELEGRAM_TOKEN,
        bisq_host=BISQ_API_HOST,
        bisq_port=BISQ_API_PORT,
        bisq_password=BISQ_API_PASSWORD
    )
    
    try:
        bot.run()
    finally:
        bot.close()

if __name__ == "__main__":
    main()

