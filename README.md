# Bisq Telegram Trading Bot

A Telegram bot that interfaces with Bisq for automated cryptocurrency trading. This bot implements a "buy low, sell high" strategy for short contracts with the goal of maximizing profits within an hour starting with $1.00.

## Features

- Telegram interface for controlling the trading bot
- Real-time balance and profit tracking
- Automated "buy low, sell high" trading strategy
- Support for short-term trading contracts
- Fee calculation and profit tracking
- Start/stop functionality via Telegram commands

## Requirements

- Python 3.9+
- Bisq daemon running with API enabled
- Telegram bot token

## Installation

1. Clone this repository:
   ```
   git clone https://github.com/yourusername/bisq-telegram-bot.git
   cd bisq-telegram-bot
   ```

2. Install the required dependencies:
   ```
   pip install python-telegram-bot grpcio grpcio-tools
   ```

3. Configure the bot:
   - Edit `bot.py` and update the following variables:
     - `TELEGRAM_TOKEN`: Your Telegram bot token
     - `BISQ_API_HOST`: Hostname where Bisq API is running (default: localhost)
     - `BISQ_API_PORT`: Port for Bisq API (default: 9998)
     - `BISQ_API_PASSWORD`: Password for Bisq API

## Running Bisq with API Enabled

Before running the bot, you need to start the Bisq daemon with API access enabled:

1. Download and install Bisq from [https://bisq.network/](https://bisq.network/)

2. Start Bisq with API enabled:
   ```
   ./bisq-desktop --apiPassword=your_api_password --apiPort=9998
   ```

   Or for headless operation:
   ```
   ./bisq-daemon --apiPassword=your_api_password --apiPort=9998
   ```

**Important Note**: Never run the Bisq API daemon and the Bisq desktop application at the same time on the same host, as they share the same wallet and connection ports.

## Running the Bot

1. Start the bot:
   ```
   python bot.py
   ```

2. Open Telegram and start a conversation with your bot by sending the `/start` command.

3. Use the provided buttons or commands to control the bot:
   - `/start` - Show the main menu
   - `/help` - Show help information
   - `/status` - Show current bot status and balance
   - `/history` - Show recent trading history

## Trading Strategy

The bot implements a "buy low, sell high" strategy based on moving averages:

1. It tracks short-term (5-minute) and long-term (15-minute) moving averages of the BTC price.
2. It buys when the price falls below the short-term moving average by a certain threshold (2% by default) and is also below the long-term moving average.
3. It sells when the price rises above the short-term moving average by a certain threshold (2% by default) or is significantly above the long-term moving average.
4. The strategy automatically calculates and accounts for trading fees.
5. It aims to maximize profits within a 1-hour timeframe starting with $1.00.

## Project Structure

- `bot.py`: Main entry point that integrates the Telegram bot with the trading functionality
- `bisq_client.py`: Client for interacting with the Bisq API
- `trading_strategy.py`: Implementation of the "buy low, sell high" trading strategy
- `main.py`: Alternative entry point with simplified functionality

## Security Considerations

- Store your API password securely and never share it
- Be cautious when running the bot on public servers
- Start with small amounts until you're confident in the bot's operation
- Regularly back up your Bisq wallet

## Disclaimer

This bot is provided for educational purposes only. Use at your own risk. Cryptocurrency trading involves significant risk and you could lose your investment. The authors are not responsible for any financial losses incurred from using this software.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

