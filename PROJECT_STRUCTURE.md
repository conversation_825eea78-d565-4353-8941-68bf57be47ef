# Bisq Telegram Trading Bot - Project Structure

## Directory Structure

```
bisq_telegram_bot/
├── bot.py                 # Main entry point integrating Telegram bot with trading functionality
├── bisq_client.py         # Client for interacting with the Bisq API
├── trading_strategy.py    # Implementation of the "buy low, sell high" trading strategy
├── main.py                # Alternative entry point with simplified functionality
├── run.sh                 # Shell script to run the bot
├── README.md              # Project documentation
└── PROJECT_STRUCTURE.md   # This file
```

## Module Descriptions

### bot.py
- Main entry point for the application
- Integrates the Telegram bot interface with the Bisq trading functionality
- Handles user commands and button interactions
- Manages the trading loop in the background

### bisq_client.py
- Client for interacting with the Bisq API
- Handles communication with the Bisq daemon via gRPC
- Provides methods for checking balances, getting market prices, and executing trades
- Handles authentication and error handling

### trading_strategy.py
- Implements the "buy low, sell high" trading strategy
- Analyzes market conditions to make trading decisions
- Tracks price history and calculates moving averages
- Executes buy and sell orders based on market conditions
- Calculates fees and profits

### main.py
- Alternative entry point with simplified functionality
- Provides a basic Telegram bot interface
- Useful for testing and development

### run.sh
- Shell script to run the bot
- Checks for required dependencies
- Provides a simple way to start the bot

## Class Hierarchy

- **BisqTradingBot** (bot.py)
  - Main class that integrates all components
  - Manages the Telegram bot interface
  - Coordinates between user commands and trading functionality

- **BisqClient** (bisq_client.py)
  - Handles communication with the Bisq API
  - Provides methods for wallet operations, market data, and trading

- **TradingStrategy** (trading_strategy.py)
  - Implements the trading strategy
  - Makes trading decisions based on market analysis
  - Executes trades and tracks performance

## Flow of Operation

1. User starts the bot using `run.sh` or `python bot.py`
2. Bot initializes the Bisq client and trading strategy
3. User interacts with the bot via Telegram commands or buttons
4. When the bot is started, it begins the trading loop:
   - Analyzes market conditions
   - Makes trading decisions based on the strategy
   - Executes trades when conditions are favorable
   - Updates the user on status and profits
5. User can check balance, view trading history, or stop the bot at any time

## Configuration

The bot is configured through variables at the top of `bot.py`:
- `TELEGRAM_TOKEN`: Telegram bot token
- `BISQ_API_HOST`: Bisq API host
- `BISQ_API_PORT`: Bisq API port
- `BISQ_API_PASSWORD`: Bisq API password

## Trading Strategy Parameters

The trading strategy parameters are defined in `trading_strategy.py`:
- `moving_average_short`: Short-term moving average period (default: 5 minutes)
- `moving_average_long`: Long-term moving average period (default: 15 minutes)
- `buy_threshold`: Price threshold for buying (default: 2% below short-term MA)
- `sell_threshold`: Price threshold for selling (default: 2% above short-term MA)
- `fee_percentage`: Trading fee percentage (default: 1%)

