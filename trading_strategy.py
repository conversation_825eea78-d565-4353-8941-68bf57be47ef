#!/usr/bin/env python3
"""
Trading Strategy Module

This module implements the "buy low, sell high" trading strategy for short contracts.
"""

import logging
import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple

from bisq_client import BisqClient

# Configure logging
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class TradingStrategy:
    """Implementation of the "buy low, sell high" trading strategy."""
    
    def __init__(self, bisq_client: BisqClient, initial_balance: float = 1.0,
                 max_duration_minutes: int = 60, fee_percentage: float = 0.01):
        """Initialize the trading strategy.
        
        Args:
            bisq_client: Initialized Bisq API client
            initial_balance: Initial balance in USD
            max_duration_minutes: Maximum duration for trading in minutes
            fee_percentage: Trading fee percentage (0.01 = 1%)
        """
        self.bisq_client = bisq_client
        self.initial_balance = initial_balance
        self.current_balance = initial_balance
        self.current_btc_balance = 0.0
        self.max_duration_minutes = max_duration_minutes
        self.fee_percentage = fee_percentage
        
        self.start_time = None
        self.end_time = None
        self.is_running = False
        self.trade_history = []
        
        # Strategy parameters
        self.price_history = []
        self.moving_average_short = 5  # 5-minute moving average
        self.moving_average_long = 15  # 15-minute moving average
        self.buy_threshold = 0.98  # Buy when price is 2% below moving average
        self.sell_threshold = 1.02  # Sell when price is 2% above moving average
        
        # Market state
        self.last_price = None
        self.current_position = "cash"  # "cash" or "btc"
    
    async def start(self) -> None:
        """Start the trading strategy."""
        if self.is_running:
            logger.warning("Trading strategy is already running")
            return
        
        self.is_running = True
        self.start_time = datetime.now()
        self.end_time = self.start_time + timedelta(minutes=self.max_duration_minutes)
        
        logger.info(f"Trading strategy started at {self.start_time}")
        logger.info(f"Initial balance: ${self.initial_balance:.2f}")
        logger.info(f"Maximum duration: {self.max_duration_minutes} minutes")
        logger.info(f"End time: {self.end_time}")
        
        # Initialize price history
        await self._update_price_history()
    
    async def stop(self) -> None:
        """Stop the trading strategy."""
        if not self.is_running:
            logger.warning("Trading strategy is not running")
            return
        
        self.is_running = False
        
        # If we're still holding BTC, sell it
        if self.current_position == "btc" and self.current_btc_balance > 0:
            logger.info("Selling remaining BTC before stopping")
            await self._execute_sell(self.current_btc_balance)
        
        logger.info(f"Trading strategy stopped")
        logger.info(f"Final balance: ${self.current_balance:.2f}")
        
        profit_loss = self.current_balance - self.initial_balance
        profit_percentage = (profit_loss / self.initial_balance) * 100
        
        logger.info(f"Profit/Loss: ${profit_loss:.2f} ({profit_percentage:.2f}%)")
    
    async def update(self) -> Dict[str, Any]:
        """Update the trading strategy with latest market data and make trading decisions.
        
        Returns:
            Dictionary with update information
        """
        if not self.is_running:
            return {"status": "not_running"}
        
        # Check if we've reached the time limit
        if datetime.now() >= self.end_time:
            await self.stop()
            return {"status": "completed", "balance": self.current_balance}
        
        # Update price history
        await self._update_price_history()
        
        # Make trading decision
        decision = await self._make_decision()
        
        # Execute trading decision
        if decision["action"] == "buy" and self.current_position == "cash":
            # Calculate amount to buy (use all available balance)
            amount_usd = self.current_balance * 0.95  # Keep 5% as reserve
            amount_btc = amount_usd / decision["price"]
            
            # Execute buy
            success = await self._execute_buy(amount_btc, decision["price"])
            if success:
                self.current_position = "btc"
        
        elif decision["action"] == "sell" and self.current_position == "btc":
            # Sell all BTC
            success = await self._execute_sell(self.current_btc_balance)
            if success:
                self.current_position = "cash"
        
        # Return current status
        time_remaining = (self.end_time - datetime.now()).total_seconds() / 60
        
        return {
            "status": "running",
            "balance": self.current_balance,
            "btc_balance": self.current_btc_balance,
            "current_price": self.last_price,
            "position": self.current_position,
            "time_remaining_minutes": time_remaining,
            "trades_executed": len(self.trade_history)
        }
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current status of the trading strategy.
        
        Returns:
            Dictionary with status information
        """
        profit_loss = self.current_balance - self.initial_balance
        profit_percentage = (profit_loss / self.initial_balance) * 100 if self.initial_balance > 0 else 0
        
        time_remaining = None
        if self.is_running and self.end_time:
            time_remaining = (self.end_time - datetime.now()).total_seconds() / 60
            if time_remaining < 0:
                time_remaining = 0
        
        return {
            "is_running": self.is_running,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "time_remaining_minutes": time_remaining,
            "initial_balance": self.initial_balance,
            "current_balance": self.current_balance,
            "btc_balance": self.current_btc_balance,
            "profit_loss": profit_loss,
            "profit_percentage": profit_percentage,
            "current_position": self.current_position,
            "current_price": self.last_price,
            "trades_executed": len(self.trade_history),
            "recent_trades": self.trade_history[-5:] if self.trade_history else []
        }
    
    async def _update_price_history(self) -> None:
        """Update price history with latest market data."""
        try:
            # Get current market price
            price = await self.bisq_client.get_market_price()
            self.last_price = price
            
            # Add to price history with timestamp
            self.price_history.append({
                "timestamp": datetime.now(),
                "price": price
            })
            
            # Keep only the last hour of price history
            one_hour_ago = datetime.now() - timedelta(hours=1)
            self.price_history = [p for p in self.price_history if p["timestamp"] >= one_hour_ago]
            
            logger.debug(f"Updated price history. Current price: ${price:.2f}")
        except Exception as e:
            logger.error(f"Error updating price history: {e}")
    
    async def _make_decision(self) -> Dict[str, Any]:
        """Make trading decision based on current market conditions.
        
        Returns:
            Dictionary with decision information
        """
        if not self.price_history or len(self.price_history) < self.moving_average_long:
            return {"action": "hold", "reason": "insufficient_data", "price": self.last_price}
        
        # Calculate short-term moving average
        short_prices = [p["price"] for p in self.price_history[-self.moving_average_short:]]
        short_ma = sum(short_prices) / len(short_prices)
        
        # Calculate long-term moving average
        long_prices = [p["price"] for p in self.price_history[-self.moving_average_long:]]
        long_ma = sum(long_prices) / len(long_prices)
        
        # Current price
        current_price = self.last_price
        
        # Calculate price relative to moving averages
        price_to_short_ma = current_price / short_ma
        price_to_long_ma = current_price / long_ma
        
        # Make decision
        if self.current_position == "cash":
            # We're holding cash, look for buying opportunity
            if price_to_short_ma < self.buy_threshold and price_to_long_ma < 1.0:
                # Price is below short MA by threshold and below long MA
                return {
                    "action": "buy",
                    "reason": "price_below_threshold",
                    "price": current_price,
                    "short_ma": short_ma,
                    "long_ma": long_ma,
                    "price_to_short_ma": price_to_short_ma
                }
        elif self.current_position == "btc":
            # We're holding BTC, look for selling opportunity
            if price_to_short_ma > self.sell_threshold or price_to_long_ma > 1.05:
                # Price is above short MA by threshold or significantly above long MA
                return {
                    "action": "sell",
                    "reason": "price_above_threshold",
                    "price": current_price,
                    "short_ma": short_ma,
                    "long_ma": long_ma,
                    "price_to_short_ma": price_to_short_ma
                }
        
        # Default: hold current position
        return {
            "action": "hold",
            "reason": "no_signal",
            "price": current_price,
            "short_ma": short_ma,
            "long_ma": long_ma,
            "price_to_short_ma": price_to_short_ma
        }
    
    async def _execute_buy(self, amount_btc: float, price: float) -> bool:
        """Execute a buy order.
        
        Args:
            amount_btc: Amount of BTC to buy
            price: Price per BTC in USD
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Calculate USD amount
            amount_usd = amount_btc * price
            
            # Calculate fee
            fee_usd = amount_usd * self.fee_percentage
            
            # Adjust BTC amount after fee
            adjusted_amount_btc = (amount_usd - fee_usd) / price
            
            # In a real implementation, this would call the Bisq API to create and take an offer
            # For now, just simulate the trade
            
            # Update balances
            self.current_balance -= amount_usd
            self.current_btc_balance += adjusted_amount_btc
            
            # Record trade
            trade_info = {
                "timestamp": datetime.now(),
                "action": "buy",
                "amount_btc": adjusted_amount_btc,
                "price": price,
                "amount_usd": amount_usd,
                "fee_usd": fee_usd
            }
            self.trade_history.append(trade_info)
            
            logger.info(f"Buy executed: {adjusted_amount_btc:.8f} BTC at ${price:.2f} (Fee: ${fee_usd:.2f})")
            return True
        except Exception as e:
            logger.error(f"Error executing buy: {e}")
            return False
    
    async def _execute_sell(self, amount_btc: float) -> bool:
        """Execute a sell order.
        
        Args:
            amount_btc: Amount of BTC to sell
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get current price
            price = self.last_price
            
            # Calculate USD amount
            amount_usd = amount_btc * price
            
            # Calculate fee
            fee_usd = amount_usd * self.fee_percentage
            
            # Adjust USD amount after fee
            adjusted_amount_usd = amount_usd - fee_usd
            
            # In a real implementation, this would call the Bisq API to create and take an offer
            # For now, just simulate the trade
            
            # Update balances
            self.current_balance += adjusted_amount_usd
            self.current_btc_balance -= amount_btc
            
            # Record trade
            trade_info = {
                "timestamp": datetime.now(),
                "action": "sell",
                "amount_btc": amount_btc,
                "price": price,
                "amount_usd": adjusted_amount_usd,
                "fee_usd": fee_usd
            }
            self.trade_history.append(trade_info)
            
            logger.info(f"Sell executed: {amount_btc:.8f} BTC at ${price:.2f} for ${adjusted_amount_usd:.2f} (Fee: ${fee_usd:.2f})")
            return True
        except Exception as e:
            logger.error(f"Error executing sell: {e}")
            return False

# Example usage
async def example_usage():
    """Example of how to use the TradingStrategy."""
    from bisq_client import BisqClient
    
    # Initialize Bisq client
    bisq_client = BisqClient()
    
    # Initialize trading strategy
    strategy = TradingStrategy(bisq_client, initial_balance=1.0, max_duration_minutes=60)
    
    try:
        # Start strategy
        await strategy.start()
        
        # Simulate running for a few iterations
        for _ in range(5):
            status = await strategy.update()
            print(f"Strategy status: {status}")
            await asyncio.sleep(1)
        
        # Get final status
        final_status = await strategy.get_status()
        print(f"Final status: {final_status}")
    finally:
        # Stop strategy
        await strategy.stop()
        
        # Close Bisq client
        bisq_client.close()

if __name__ == "__main__":
    asyncio.run(example_usage())

