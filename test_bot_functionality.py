#!/usr/bin/env python3
"""
Comprehensive test script for Bisq Telegram Bot functionality.
"""

import asyncio
import logging
import sys
from datetime import datetime

# Configure logging
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def test_bisq_client_detailed():
    """Test BisqClient with detailed output."""
    logger.info("=== Testing BisqClient ===")
    
    try:
        from bisq_client import BisqClient
        
        client = BisqClient(host="localhost", port="9998", password="test_password")
        logger.info("✅ BisqClient initialized")
        
        # Test balances
        balances = await client.get_balances()
        logger.info(f"✅ Balances retrieved: BTC={balances['btc']:.8f}, BSQ={balances['bsq']:.2f}")
        
        # Test market price
        price = await client.get_market_price()
        logger.info(f"✅ Market price retrieved: ${price:.2f}")
        
        # Test offers
        offers = await client.get_offers()
        logger.info(f"✅ Offers retrieved: {len(offers)} offers available")
        
        # Test buy offers specifically
        buy_offers = await client.get_offers(direction="BUY")
        logger.info(f"✅ Buy offers: {len(buy_offers)} available")
        
        # Test sell offers specifically
        sell_offers = await client.get_offers(direction="SELL")
        logger.info(f"✅ Sell offers: {len(sell_offers)} available")
        
        client.close()
        logger.info("✅ BisqClient test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ BisqClient test failed: {e}")
        return False

async def test_trading_strategy_detailed():
    """Test TradingStrategy with detailed output."""
    logger.info("=== Testing TradingStrategy ===")
    
    try:
        from bisq_client import BisqClient
        from trading_strategy import TradingStrategy
        
        client = BisqClient()
        strategy = TradingStrategy(client, initial_balance=1.0, max_duration_minutes=60)
        logger.info("✅ TradingStrategy initialized")
        
        # Test starting strategy
        await strategy.start()
        logger.info("✅ Strategy started")
        
        # Test getting status
        status = await strategy.get_status()
        logger.info(f"✅ Status retrieved:")
        logger.info(f"   - Running: {status['is_running']}")
        logger.info(f"   - Balance: ${status['current_balance']:.2f}")
        logger.info(f"   - BTC Balance: {status['btc_balance']:.8f}")
        logger.info(f"   - Position: {status['current_position']}")
        logger.info(f"   - Time remaining: {status['time_remaining_minutes']:.1f} minutes")
        
        # Test multiple updates to see price changes
        logger.info("Testing strategy updates...")
        for i in range(3):
            update_result = await strategy.update()
            logger.info(f"   Update {i+1}: Price=${update_result['current_price']:.2f}, Position={update_result['position']}")
            await asyncio.sleep(0.1)  # Small delay
        
        # Test stopping strategy
        await strategy.stop()
        logger.info("✅ Strategy stopped")
        
        # Get final status
        final_status = await strategy.get_status()
        logger.info(f"✅ Final status: Balance=${final_status['current_balance']:.2f}, P/L=${final_status['profit_loss']:.2f}")
        
        client.close()
        logger.info("✅ TradingStrategy test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ TradingStrategy test failed: {e}")
        return False

async def test_bot_initialization():
    """Test bot initialization without starting Telegram polling."""
    logger.info("=== Testing Bot Initialization ===")
    
    try:
        from bot import BisqTradingBot
        
        # Test with dummy credentials
        bot = BisqTradingBot(
            token="dummy_token_for_testing",
            bisq_host="localhost",
            bisq_port="9998",
            bisq_password="test_password"
        )
        logger.info("✅ Bot initialized successfully")
        
        # Check if handlers are set up
        handler_count = len(bot.application.handlers)
        logger.info(f"✅ Bot has {handler_count} handlers configured")
        
        # Test strategy initialization
        if bot.strategy:
            logger.info("✅ Trading strategy component initialized")
        
        # Test bisq client initialization
        if bot.bisq_client:
            logger.info("✅ Bisq client component initialized")
        
        # Clean up
        bot.close()
        logger.info("✅ Bot initialization test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Bot initialization test failed: {e}")
        return False

async def test_integration():
    """Test integration between components."""
    logger.info("=== Testing Component Integration ===")
    
    try:
        from bot import BisqTradingBot
        
        bot = BisqTradingBot(
            token="dummy_token",
            bisq_host="localhost", 
            bisq_port="9998",
            bisq_password="test_password"
        )
        
        # Test that bot can get strategy status
        status = await bot.strategy.get_status()
        logger.info(f"✅ Bot can access strategy status: Balance=${status['current_balance']:.2f}")
        
        # Test that strategy can access bisq client
        price = await bot.bisq_client.get_market_price()
        logger.info(f"✅ Strategy can access market data: ${price:.2f}")
        
        bot.close()
        logger.info("✅ Integration test completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Integration test failed: {e}")
        return False

async def main():
    """Run all functionality tests."""
    logger.info("🚀 Starting Bisq Telegram Bot Functionality Tests")
    logger.info("=" * 60)
    
    tests = [
        ("BisqClient", test_bisq_client_detailed),
        ("TradingStrategy", test_trading_strategy_detailed),
        ("Bot Initialization", test_bot_initialization),
        ("Component Integration", test_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running {test_name} test...")
        try:
            if await test_func():
                passed += 1
                logger.info(f"✅ {test_name} test PASSED")
            else:
                logger.error(f"❌ {test_name} test FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name} test FAILED with exception: {e}")
    
    logger.info("\n" + "=" * 60)
    logger.info(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Bot functionality is working correctly.")
        return True
    else:
        logger.error(f"⚠️  {total - passed} test(s) failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("Tests interrupted by user")
        sys.exit(1)
