#!/usr/bin/env python3
"""
Test script for the Bisq Telegram Trading Bot.

This script verifies that all modules can be imported correctly and the basic
functionality works.
"""

import sys
import logging
import asyncio

# Configure logging
logging.basicConfig(
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    level=logging.INFO
)
logger = logging.getLogger(__name__)

def test_imports():
    """Test that all required modules can be imported."""
    try:
        import telegram
        import grpc
        logger.info("External dependencies imported successfully")
        
        from bisq_client import BisqClient
        from trading_strategy import TradingStrategy
        logger.info("Project modules imported successfully")
        
        return True
    except ImportError as e:
        logger.error(f"Import error: {e}")
        return False

async def test_bisq_client():
    """Test the BisqClient class."""
    try:
        from bisq_client import BisqClient
        
        client = BisqClient(host="localhost", port="9998", password="api_password")
        logger.info("BisqClient initialized successfully")
        
        # Test getting balances
        balances = await client.get_balances()
        logger.info(f"Balances: {balances}")
        
        # Test getting market price
        price = await client.get_market_price()
        logger.info(f"Current BTC/USD price: ${price}")
        
        # Test getting offers
        offers = await client.get_offers()
        logger.info(f"Got {len(offers)} offers")
        
        client.close()
        return True
    except Exception as e:
        logger.error(f"BisqClient test error: {e}")
        return False

async def test_trading_strategy():
    """Test the TradingStrategy class."""
    try:
        from bisq_client import BisqClient
        from trading_strategy import TradingStrategy
        
        client = BisqClient()
        strategy = TradingStrategy(client, initial_balance=1.0, max_duration_minutes=60)
        logger.info("TradingStrategy initialized successfully")
        
        # Test starting the strategy
        await strategy.start()
        logger.info("Strategy started successfully")
        
        # Test getting status
        status = await strategy.get_status()
        logger.info(f"Strategy status: {status}")
        
        # Test updating the strategy
        update_result = await strategy.update()
        logger.info(f"Strategy update result: {update_result}")
        
        # Test stopping the strategy
        await strategy.stop()
        logger.info("Strategy stopped successfully")
        
        client.close()
        return True
    except Exception as e:
        logger.error(f"TradingStrategy test error: {e}")
        return False

async def test_telegram_bot():
    """Test the Telegram bot initialization."""
    try:
        from bot import BisqTradingBot

        # Test bot initialization (without actually starting it)
        bot = BisqTradingBot(
            token="test_token",
            bisq_host="localhost",
            bisq_port="9998",
            bisq_password="test_password"
        )
        logger.info("BisqTradingBot initialized successfully")

        # Test that handlers are set up
        if bot.application.handlers:
            logger.info(f"Bot has {len(bot.application.handlers)} handlers configured")

        # Clean up
        bot.close()
        return True
    except Exception as e:
        logger.error(f"Telegram bot test error: {e}")
        return False

async def run_tests():
    """Run all tests."""
    logger.info("Starting tests...")

    # Test imports
    if not test_imports():
        logger.error("Import tests failed")
        return False

    # Test BisqClient
    if not await test_bisq_client():
        logger.error("BisqClient tests failed")
        return False

    # Test TradingStrategy
    if not await test_trading_strategy():
        logger.error("TradingStrategy tests failed")
        return False

    # Test Telegram Bot
    if not await test_telegram_bot():
        logger.error("Telegram bot tests failed")
        return False

    logger.info("All tests passed!")
    return True

if __name__ == "__main__":
    try:
        result = asyncio.run(run_tests())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("Tests interrupted by user")
        sys.exit(1)

